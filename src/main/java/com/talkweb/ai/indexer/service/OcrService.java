package com.talkweb.ai.indexer.service;

import com.talkweb.ai.indexer.config.OcrConfiguration;
import com.talkweb.ai.indexer.model.OcrResult;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import net.sourceforge.tess4j.Word;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.awt.Rectangle;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * OCR服务类
 *
 * 基于Tesseract OCR引擎的文本识别服务，支持多语言识别、
 * 置信度评估、异步处理和详细的结果分析。
 *
 * <AUTHOR> Assistant
 * @version 1.0
 */
@Service
public class OcrService {

    private static final Logger logger = LoggerFactory.getLogger(OcrService.class);

    private final OcrConfiguration ocrConfig;
    private ITesseract tesseract;
    private ExecutorService executorService;

    @Autowired
    public OcrService(OcrConfiguration ocrConfig) {
        this.ocrConfig = ocrConfig;
    }

    @PostConstruct
    public void initialize() {
        if (!ocrConfig.isEnabled()) {
            logger.info("OCR service is disabled");
            return;
        }

        try {
            logger.info("Initializing OCR service with configuration: {}", ocrConfig);

            // 初始化Tesseract实例
            tesseract = new Tesseract();

            // 设置数据路径
            if (ocrConfig.getDataPath() != null && !ocrConfig.getDataPath().isEmpty()) {
                tesseract.setDatapath(ocrConfig.getDataPath());
                logger.info("Using custom Tesseract data path: {}", ocrConfig.getDataPath());
            }

            // 设置语言
            tesseract.setLanguage(ocrConfig.getLanguageString());
            logger.info("OCR languages set to: {}", ocrConfig.getLanguageString());

            // 设置页面分割模式
            tesseract.setPageSegMode(ocrConfig.getPageSegmentationMode());

            // 设置OCR引擎模式
            tesseract.setOcrEngineMode(ocrConfig.getOcrEngineMode());

            // 设置自定义变量
            for (Map.Entry<String, String> entry : ocrConfig.getCustomVariables().entrySet()) {
                tesseract.setVariable(entry.getKey(), entry.getValue());
            }

            // 初始化线程池
            executorService = Executors.newVirtualThreadPerTaskExecutor();

            logger.info("OCR service initialized successfully");

        } catch (Exception e) {
            logger.error("Failed to initialize OCR service", e);
            throw new RuntimeException("OCR service initialization failed", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        logger.info("OCR service shutdown completed");
    }

    /**
     * 对图像文件执行OCR识别
     *
     * @param imageFile 图像文件
     * @return OCR识别结果
     */
    public OcrResult recognizeText(File imageFile) {
        if (!ocrConfig.isEnabled()) {
            return OcrResult.failure("OCR service is disabled");
        }

        if (imageFile == null || !imageFile.exists()) {
            return OcrResult.failure("Image file does not exist");
        }

        long startTime = System.currentTimeMillis();

        try {
            logger.debug("Starting OCR recognition for file: {}", imageFile.getName());

            // 执行OCR识别
            String text = tesseract.doOCR(imageFile);

            // 获取详细结果（包含置信度和位置信息）
            // Note: getWords with File parameter is not available, use BufferedImage instead
            List<Word> words = null;
            try {
                BufferedImage bufferedImage = javax.imageio.ImageIO.read(imageFile);
                if (bufferedImage != null) {
                    words = tesseract.getWords(bufferedImage, 1); // 1 = WORD level
                }
            } catch (Exception e) {
                logger.warn("Failed to get word-level results for file: {}", imageFile.getName(), e);
            }

            // 计算整体置信度
            float overallConfidence = calculateOverallConfidence(words);

            // 创建结果对象
            OcrResult result = createOcrResult(text, overallConfidence, words, startTime);

            // 添加配置信息
            result.setOcrConfig(createConfigInfo());

            logger.debug("OCR recognition completed for file: {}, confidence: {}, processing time: {}ms",
                        imageFile.getName(), overallConfidence, result.getProcessingTimeMs());

            return result;

        } catch (TesseractException e) {
            logger.error("OCR recognition failed for file: {}", imageFile.getName(), e);
            return OcrResult.failure("OCR recognition failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during OCR recognition for file: {}", imageFile.getName(), e);
            return OcrResult.failure("Unexpected error: " + e.getMessage());
        }
    }

    /**
     * 对BufferedImage执行OCR识别
     *
     * @param image BufferedImage对象
     * @return OCR识别结果
     */
    public OcrResult recognizeText(BufferedImage image) {
        if (!ocrConfig.isEnabled()) {
            return OcrResult.failure("OCR service is disabled");
        }

        if (image == null) {
            return OcrResult.failure("Image is null");
        }

        long startTime = System.currentTimeMillis();

        try {
            logger.debug("Starting OCR recognition for BufferedImage");

            // 执行OCR识别
            String text = tesseract.doOCR(image);

            // 获取详细结果
            List<Word> words = tesseract.getWords(image, 1);

            // 计算整体置信度
            float overallConfidence = calculateOverallConfidence(words);

            // 创建结果对象
            OcrResult result = createOcrResult(text, overallConfidence, words, startTime);

            // 添加配置信息
            result.setOcrConfig(createConfigInfo());

            logger.debug("OCR recognition completed for BufferedImage, confidence: {}, processing time: {}ms",
                        overallConfidence, result.getProcessingTimeMs());

            return result;

        } catch (TesseractException e) {
            logger.error("OCR recognition failed for BufferedImage", e);
            return OcrResult.failure("OCR recognition failed: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during OCR recognition for BufferedImage", e);
            return OcrResult.failure("Unexpected error: " + e.getMessage());
        }
    }

    /**
     * 异步执行OCR识别
     *
     * @param imageFile 图像文件
     * @return CompletableFuture包装的OCR结果
     */
    public CompletableFuture<OcrResult> recognizeTextAsync(File imageFile) {
        return CompletableFuture
            .supplyAsync(() -> recognizeText(imageFile), executorService)
            .orTimeout(ocrConfig.getTimeoutSeconds(), TimeUnit.SECONDS)
            .exceptionally(throwable -> {
                if (throwable instanceof TimeoutException) {
                    logger.warn("OCR recognition timeout for file: {}", imageFile.getName());
                    return OcrResult.timeout();
                } else {
                    logger.error("Async OCR recognition failed for file: {}", imageFile.getName(), throwable);
                    return OcrResult.failure("Async OCR failed: " + throwable.getMessage());
                }
            });
    }

    /**
     * 异步执行OCR识别
     *
     * @param image BufferedImage对象
     * @return CompletableFuture包装的OCR结果
     */
    public CompletableFuture<OcrResult> recognizeTextAsync(BufferedImage image) {
        return CompletableFuture
            .supplyAsync(() -> recognizeText(image), executorService)
            .orTimeout(ocrConfig.getTimeoutSeconds(), TimeUnit.SECONDS)
            .exceptionally(throwable -> {
                if (throwable instanceof TimeoutException) {
                    logger.warn("OCR recognition timeout for BufferedImage");
                    return OcrResult.timeout();
                } else {
                    logger.error("Async OCR recognition failed for BufferedImage", throwable);
                    return OcrResult.failure("Async OCR failed: " + throwable.getMessage());
                }
            });
    }

    /**
     * 检查OCR服务是否可用
     *
     * @return 如果服务可用返回true
     */
    public boolean isAvailable() {
        return ocrConfig.isEnabled() && tesseract != null;
    }

    /**
     * 获取OCR配置信息
     *
     * @return 配置信息映射
     */
    public Map<String, Object> getConfigInfo() {
        return createConfigInfo();
    }

    // Private helper methods

    private float calculateOverallConfidence(List<Word> words) {
        if (words == null || words.isEmpty()) {
            return 0.0f;
        }

        float totalConfidence = 0.0f;
        int validWords = 0;

        for (Word word : words) {
            if (word.getConfidence() > 0) {
                totalConfidence += word.getConfidence();
                validWords++;
            }
        }

        return validWords > 0 ? totalConfidence / validWords : 0.0f;
    }

    private OcrResult createOcrResult(String text, float confidence, List<Word> words, long startTime) {
        long processingTime = System.currentTimeMillis() - startTime;

        // 确定状态
        OcrResult.Status status;
        if (confidence >= ocrConfig.getConfidenceThreshold()) {
            status = OcrResult.Status.SUCCESS;
        } else if (confidence > 0) {
            status = OcrResult.Status.LOW_CONFIDENCE;
        } else {
            status = OcrResult.Status.FAILED;
        }

        OcrResult result = new OcrResult(status, text != null ? text.trim() : "", confidence);
        result.setProcessingTimeMs(processingTime);

        // 转换单词结果
        if (words != null && !words.isEmpty()) {
            List<OcrResult.WordResult> wordResults = new ArrayList<>();
            for (Word word : words) {
                Rectangle boundingBox = word.getBoundingBox();
                wordResults.add(new OcrResult.WordResult(
                    word.getText(),
                    word.getConfidence(),
                    boundingBox
                ));
            }
            result.setWords(wordResults);
        }

        return result;
    }

    private Map<String, Object> createConfigInfo() {
        Map<String, Object> config = new HashMap<>();
        config.put("languages", ocrConfig.getLanguages());
        config.put("pageSegmentationMode", ocrConfig.getPageSegmentationMode());
        config.put("ocrEngineMode", ocrConfig.getOcrEngineMode());
        config.put("confidenceThreshold", ocrConfig.getConfidenceThreshold());
        config.put("preprocessingEnabled", ocrConfig.isPreprocessingEnabled());
        config.put("timeoutSeconds", ocrConfig.getTimeoutSeconds());
        return config;
    }
}
