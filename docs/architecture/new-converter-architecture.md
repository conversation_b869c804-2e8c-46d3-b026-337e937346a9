# 新转换器架构指南

## 概述

本文档描述了作为重构项目一部分实现的新转换器架构。新架构提供了更好的关注点分离、改进的性能和增强的可维护性。

## 架构组件

### 1. 核心接口

#### BaseConverter<T, R>
所有转换器的基础接口。

```java
public interface BaseConverter<T, R> {
    R convert(T input) throws ConversionException;
    boolean supports(T input);
}
```

#### DocumentConverter
文档转换操作的专用接口。

```java
public interface DocumentConverter extends BaseConverter<File, ConversionResult> {
    ConversionResult convert(File input, ConversionContext context) throws ConversionException;
    Set<String> getSupportedExtensions();
    ConversionCapabilities getCapabilities();
    ConversionMetadata getMetadata();
}
```

### 2. 抽象基类

#### AbstractDocumentConverter
为所有文档转换器提供通用功能：

- **缓存**: 具有可配置策略的自动结果缓存
- **验证**: 输入验证和错误处理
- **性能**: 动态缓存大小调整和内存优化
- **模板方法**: 一致的转换流程

```java
public abstract class AbstractDocumentConverter implements DocumentConverter {
    @Override
    public final ConversionResult convert(File input, ConversionContext context) throws ConversionException {
        // 1. 验证输入
        validateInput(input, context);

        // 2. 检查缓存
        String cacheKey = getCacheKey(input, context);
        ConversionResult cachedResult = cache.get(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }

        // 3. 执行转换
        ConversionResult result = doConvert(input, context);

        // 4. 缓存结果
        if (result != null && result.isSuccess()) {
            cache.put(cacheKey, result);
        }

        return result;
    }

    protected abstract ConversionResult doConvert(File input, ConversionContext context) throws ConversionException;
}
```

### 3. 适配器模式

#### ConverterPluginAdapter
在新转换器架构和现有插件系统之间架起桥梁：

```java
public class ConverterPluginAdapter implements Plugin {
    private final DocumentConverter converter;
    private final PluginMetadata metadata;

    @Override
    public ProcessingResult process(File inputFile, ProcessingContext context) throws DocumentProcessingException {
        // 将新上下文转换为旧上下文
        ConversionContext conversionContext = adaptContext(context);

        // 执行转换
        ConversionResult result = converter.convert(inputFile, conversionContext);

        // 将结果转换回旧格式
        return adaptResult(result);
    }
}
```

### 4. 配置和上下文

#### ConversionContext
为转换提供灵活的配置选项：

```java
public class ConversionContext {
    private final ConversionOptions options;
    private final String mode;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        public Builder option(String key, Object value) { ... }
        public Builder mode(String mode) { ... }
        public ConversionContext build() { ... }
    }
}
```

#### ConversionCapabilities
描述转换器能做什么：

```java
public class ConversionCapabilities {
    public enum Features {
        HEADINGS, TABLES, LISTS, IMAGES, METADATA
    }

    public static Builder builder() {
        return new Builder();
    }

    public boolean hasFeature(Features feature) { ... }
    public <T> T getCapability(String name, Class<T> type, T defaultValue) { ... }
}
```

## 性能优化

### 1. 动态缓存

新架构包含适应系统条件的智能缓存：

- **内存感知大小调整**: 缓存大小根据可用内存调整
- **LRU 淘汰**: 最近最少使用的项目首先被淘汰
- **TTL 支持**: 缓存项目的自动过期

### 2. 流式处理

对于大文件，架构支持流式处理：

```java
StreamingProcessor processor = new StreamingProcessor(bufferSize, useDirectBuffers);
processor.processFileStreaming(inputFile, chunkProcessor, outputFile);
```

### 3. 并发处理

使用虚拟线程增强并发处理：

```java
ConcurrentProcessingService service = new ConcurrentProcessingService();
List<ConversionResult> results = service.processParallel(files, converter::convert);
```

## 迁移指南

### 转换旧转换器

1. **继承 AbstractDocumentConverter** 而不是直接实现 Plugin
2. **实现 doConvert()** 方法，包含您的转换逻辑
3. **在 getCapabilities() 方法中定义功能**
4. **更新配置** 以使用 ConverterPluginAdapterFactory

#### 之前（旧架构）：
```java
public class MyConverter implements Plugin, DocumentConverter {
    public ProcessingResult process(File inputFile, ProcessingContext context) {
        // 转换逻辑与插件生命周期混合
    }
}
```

#### 之后（新架构）：
```java
public class MyConverter extends AbstractDocumentConverter {
    @Override
    protected ConversionResult doConvert(File inputFile, ConversionContext context) {
        // 纯转换逻辑
    }

    @Override
    public ConversionCapabilities getCapabilities() {
        return ConversionCapabilities.builder()
                .feature(ConversionCapabilities.Features.HEADINGS)
                .feature(ConversionCapabilities.Features.TABLES)
                .build();
    }
}
```

### 配置更新

更新您的 Spring 配置以使用适配器工厂：

```java
@Bean
public Plugin myConverterPlugin() {
    MyConverter converter = new MyConverter();
    PluginMetadata metadata = PluginMetadata.builder()
            .id("my-converter")
            .name("我的文档转换器")
            .version("3.0.0")
            .description("使用新架构转换文档")
            .className(MyConverter.class.getName())
            .build();

    return ConverterPluginAdapterFactory.createAdapter(converter, metadata);
}
```

## 最佳实践

### 1. 转换器实现

- **保持 doConvert() 纯净**: 只包含转换逻辑，不包含缓存或验证
- **使用 ConversionContext**: 通过上下文对象访问配置
- **优雅地处理错误**: 抛出带有有意义消息的 ConversionException
- **支持功能**: 准确描述您的转换器能做什么

### 2. 性能考虑

- **对大文件使用流式处理**: 检查文件大小，适当时使用 StreamingProcessor
- **适当配置缓存**: 设置合理的缓存大小和 TTL 值
- **监控内存使用**: 使用 PerformanceOptimizer 进行内存感知处理

### 3. 测试

- **测试转换逻辑**: 专注于 doConvert() 方法
- **使用各种上下文测试**: 验证不同配置选项下的行为
- **测试错误条件**: 确保正确的异常处理
- **测试性能**: 验证缓存和流式处理正常工作

## 示例

查看 `examples/` 目录中的完整工作示例：

- 基本转换器实现
- 具有流式支持的高级转换器
- 性能优化技术
- 测试策略

## 故障排除

### 常见问题

1. **编译错误**: 确保所有导入都更新到新包
2. **缓存不工作**: 验证缓存配置和键生成
3. **性能问题**: 检查是否应该对大文件使用流式处理
4. **插件未加载**: 验证 Spring 上下文中的适配器配置

### 调试技巧

- 为 `com.talkweb.ai.indexer.core` 包启用调试日志
- 使用 PerformanceOptimizer 指标监控系统健康状况
- 使用 ConversionCacheManager.getAllCacheStatistics() 检查缓存统计信息

## 未来增强

新架构设计为支持未来增强：

- **插件热重载**: 转换器的动态加载/卸载
- **分布式处理**: 支持分布式转换工作负载
- **高级缓存**: Redis 或其他外部缓存后端
- **指标集成**: Prometheus/Micrometer 指标支持
